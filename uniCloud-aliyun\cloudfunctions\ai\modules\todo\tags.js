/**
 * Todo 标签管理模块
 * 负责标签的 CRUD 及运维操作（重命名、合并）
 * 与 Python 版 tag_tools.py 对齐
 */

const { API_CONFIG, ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  removeEmptyFields,
} = require('./utils')

/**
 * 标签管理类
 */
class TagManager {
  constructor(authManager) {
    this.authManager = authManager
  }

  /**
   * 获取标签列表
   * 与 Python 版保持一致：从 batch/check/0 读取 tags 并原样返回
   */
  async getTags() {
    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TagManager.getTags] 获取基础数据失败：', batchResult)
        return batchResult
      }
      const { tags = [] } = batchResult.data
      // 直接返回（不做任务展开）
      return createSuccessResponse('获取标签列表成功', Array.isArray(tags) ? tags : [])
    } catch (error) {
      console.error('[TagManager.getTags] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取标签列表失败', error)
    }
  }

  /**
   * 创建标签
   * 对齐 Python：使用批量接口 /api/v2/batch/tag
   * 仅在必要字段存在时发送
   */
  async createTag(options = {}) {
    const { name = null, color = null } = options

    const validation = validateParams({ name }, ['name'])
    if (validation) {
      console.warn('[TagManager.createTag] 参数校验失败：', validation)
      return validation
    }

    try {
      const addPayload = removeEmptyFields({
        name: name,
        label: name,
        color: color,
        sortOrder: 0,
        sortType: 'name',
        parent: null,
        type: 1,
      })

      const payload = {
        add: [addPayload],
        update: [],
        delete: [],
      }

      const result = await this.authManager._request('POST', API_CONFIG.BATCH_TAG_URL, payload)
      if (result.errCode) {
        console.error('[TagManager.createTag] 创建标签失败：', result)
        return result
      }
      // 返回我们提交的 add 数据，保持与 Python 版一致
      return createSuccessResponse('标签创建成功', addPayload)
    } catch (error) {
      console.error('[TagManager.createTag] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建标签失败', error)
    }
  }

  /**
   * 更新标签
   * - 若需要重命名，先调用 /api/v2/tag/rename
   * - 再使用 /api/v2/batch/tag 更新颜色等其它属性
   */
  async updateTag(options = {}) {
    const { tagIdOrName = null, name = undefined, color = undefined } = options

    const validation = validateParams({ tagIdOrName }, ['tagIdOrName'])
    if (validation) {
      console.warn('[TagManager.updateTag] 参数校验失败：', validation)
      return validation
    }

    try {
      // 读取现有标签，按名称定位（与 Python 简化一致）
      const tagsResult = await this.getTags()
      if (tagsResult.errCode) return tagsResult
      const tags = tagsResult.data || []
      const current = tags.find((t) => (t.name || '') === tagIdOrName)
      if (!current) {
        return createErrorResponse(ERROR_CODES.TAG_NOT_FOUND, `未找到名称为 '${tagIdOrName}' 的标签`)
      }

      let effectiveName = current.name
      // 需要重命名
      if (name && name !== effectiveName) {
        const renamePayload = { name: effectiveName, newName: name }
        const renameRes = await this.authManager._request('PUT', API_CONFIG.TAG_RENAME_URL, renamePayload)
        if (renameRes.errCode) {
          console.error('[TagManager.updateTag] 重命名失败：', renameRes)
          return renameRes
        }
        effectiveName = name
      }

      // 组装更新数据（保持其它属性）
      const updateObj = removeEmptyFields({
        name: effectiveName,
        label: effectiveName,
        color: color !== undefined ? color : current.color,
        sortOrder: current.sortOrder ?? 0,
        sortType: current.sortType || 'name',
        parent: null,
        type: current.type ?? 1,
      })

      const payload = {
        add: [],
        update: [updateObj],
        delete: [],
      }

      const result = await this.authManager._request('POST', API_CONFIG.BATCH_TAG_URL, payload)
      if (result.errCode) {
        console.error('[TagManager.updateTag] 更新标签失败：', result)
        return result
      }

      return createSuccessResponse('标签更新成功', updateObj)
    } catch (error) {
      console.error('[TagManager.updateTag] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新标签失败', error)
    }
  }

  /**
   * 删除标签
   * - 与 Python 版一致：先定位标签，再调用 /api/v2/tag/delete
   */
  async deleteTag(options = {}) {
    const { tagIdOrName = null } = options

    const validation = validateParams({ tagIdOrName }, ['tagIdOrName'])
    if (validation) {
      console.warn('[TagManager.deleteTag] 参数校验失败：', validation)
      return validation
    }

    try {
      // 读取标签信息（主要用于返回）
      const tagsResult = await this.getTags()
      if (tagsResult.errCode) return tagsResult
      const tags = tagsResult.data || []
      const target = tags.find((t) => (t.name || '') === tagIdOrName)
      if (!target) {
        return createErrorResponse(ERROR_CODES.TAG_NOT_FOUND, `未找到名称为 '${tagIdOrName}' 的标签`)
      }

      const payload = { name: tagIdOrName }
      // 使用 POST 以确保请求体被发送（与 Python 行为一致）
      const result = await this.authManager._request('POST', API_CONFIG.TAG_DELETE_URL, payload)
      if (result.errCode) {
        console.error('[TagManager.deleteTag] 删除标签失败：', result)
        return result
      }

      return createSuccessResponse('标签删除成功', target)
    } catch (error) {
      console.error('[TagManager.deleteTag] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除标签失败', error)
    }
  }

  /**
   * 重命名标签
   */
  async renameTag(options = {}) {
    const { oldName = null, newName = null } = options

    const validation = validateParams({ oldName, newName }, ['oldName', 'newName'])
    if (validation) {
      console.warn('[TagManager.renameTag] 参数校验失败：', validation)
      return validation
    }

    try {
      const payload = { name: oldName, newName: newName }
      const result = await this.authManager._request('PUT', API_CONFIG.TAG_RENAME_URL, payload)
      if (result.errCode) {
        console.error('[TagManager.renameTag] 重命名失败：', result)
        return result
      }
      return createSuccessResponse(`成功将标签从 '${oldName}' 重命名为 '${newName}'`, { old_name: oldName, new_name: newName })
    } catch (error) {
      console.error('[TagManager.renameTag] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '重命名标签失败', error)
    }
  }

  /**
   * 合并标签
   */
  async mergeTags(options = {}) {
    const { sourceName = null, targetName = null } = options

    const validation = validateParams({ sourceName, targetName }, ['sourceName', 'targetName'])
    if (validation) {
      console.warn('[TagManager.mergeTags] 参数校验失败：', validation)
      return validation
    }

    try {
      const payload = { fromName: sourceName, toName: targetName }
      const result = await this.authManager._request('PUT', API_CONFIG.TAG_MERGE_URL, payload)
      if (result.errCode) {
        console.error('[TagManager.mergeTags] 合并失败：', result)
        return result
      }
      return createSuccessResponse(`成功将标签 '${sourceName}' 合并到 '${targetName}'`, {
        source_tag: sourceName,
        target_tag: targetName,
      })
    } catch (error) {
      console.error('[TagManager.mergeTags] 错误：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '合并标签失败', error)
    }
  }
}

module.exports = TagManager


