# 时间表达处理规则测试用例

## 🔧 关键问题修复

### 问题根源
用户发现即使没有写具体的时间段，系统还是会自动添加具体的时间点。问题出在 `executeCreateTask` 函数中的硬编码逻辑：

**原来的问题逻辑**：
```javascript
// 原来的逻辑会为所有包含日期的表达自动添加默认时间
if (taskText.includes('明天')) {
  processedDueDate = formatLocalDateTime(tomorrow, 18, 0, 0) // 自动添加18:00
}
if (taskText.includes('今天')) {
  processedDueDate = formatLocalDateTime(now, 18, 0, 0) // 自动添加18:00
}
```

**修复后的逻辑**：
```javascript
// 现在只有包含具体时间段的表达才设置具体时间
if (taskText.includes('明天下午')) {
  processedDueDate = formatLocalDateTime(tomorrow, 15, 0, 0) // 只有明确说"下午"才添加15:00
}
// "明天"这种仅包含日期的表达不再自动添加时间
```

### 最终修复方案（彻底解决）
1. **完全移除硬编码时间处理逻辑**：删除了所有在 `executeCreateTask` 函数中的硬编码时间处理
2. **完全由 AI 处理时间**：所有时间识别和转换都由 AI 根据 system prompt 的指导来完成
3. **确保一致性**：避免了硬编码逻辑与 AI 逻辑的冲突和不一致

### 为什么要完全移除硬编码逻辑？
1. **不完整的覆盖**：硬编码逻辑无法覆盖所有时间表达（如"后天"、"前天"等）
2. **逻辑冲突**：硬编码逻辑可能与 AI 的判断产生冲突
3. **维护困难**：需要同时维护硬编码规则和 system prompt 规则
4. **灵活性差**：硬编码逻辑难以处理复杂和变化的时间表达

## 修改内容总结

### 1. 在 system prompt 中添加了详细的时间表达处理规则

#### 新增的处理规则包括：

1. **时间表达识别与提取**：
   - 识别时间关键词：今天、明天、后天、昨天、前天
   - 识别周期表达：本周、下周、上周、本月、下月、上月
   - 识别具体时间段：早上、上午、中午、下午、晚上、深夜
   - 识别星期表达：周一到周日

2. **任务标题清理规则**：
   - 从任务标题中移除已识别的时间关键词
   - 移除多余的连接词：要、需要、准备、计划、安排等
   - 保持标题简洁明确，突出核心任务内容

3. **时间参数转换规则**：
   - 将识别的时间表达转换为对应的 dueDate 或 startDate 参数
   - 使用 YYYY-MM-DD HH:MM:SS 格式
   - 默认时间设置：早上09:00，上午10:00，中午12:00，下午15:00，晚上18:00，深夜23:59

### 2. 扩展了时间计算逻辑

在主函数和 `executeGetCurrentTimeInfo` 函数中都添加了更完整的时间计算：

- 后天、前天的计算
- 本周末（周六）和这个周末（周日）的区分
- 本月底、下个月初、下月底的计算

### 3. 提供了详细的处理示例

#### 正确处理示例：
- 输入："添加任务：明天要开股东大会"
- 解析：title="开股东大会", dueDate="2025-08-08 18:00:00"

- 输入："添加任务：本月完成学习计划"
- 解析：title="完成学习计划", dueDate="本月最后一天 23:59:59"

- 输入："明天早上9点开会"
- 解析：title="开会", dueDate="2025-08-08 09:00:00"

#### 错误处理示例：
- ❌ 不要将时间词包含在标题中：title="明天要开股东大会"
- ❌ 不要忽略时间信息而不设置 dueDate

## 新增的时间精度处理规则

### 4. 时间精度处理规则
- **包含具体时间段的表达**：设置具体时间（YYYY-MM-DD HH:MM:SS）
- **仅包含日期的表达**：使用日期格式（YYYY-MM-DD，全天任务）

## 测试用例

### 时间精度处理测试（新增）

#### A. 仅包含日期的表达（全天任务）
1. "添加任务：今天完成报告" → title="完成报告", dueDate="2025-08-07"
2. "添加任务：明天要开会" → title="开会", dueDate="2025-08-08"
3. "添加任务：后天交作业" → title="交作业", dueDate="2025-08-09"
4. "需要明天准备材料" → title="准备材料", dueDate="2025-08-08"
5. "本月底完成项目" → title="完成项目", dueDate="2025-08-31"

#### B. 包含具体时间段的表达（具体时间）
6. "今天下午开会" → title="开会", dueDate="2025-08-07 15:00:00"
7. "明天早上9点开会" → title="开会", dueDate="2025-08-08 09:00:00"
8. "今晚要写代码" → title="写代码", dueDate="2025-08-07 23:59:59"
9. "下周一下午开会" → title="开会", dueDate="下周一 15:00:00"
10. "明天晚上聚餐" → title="聚餐", dueDate="2025-08-08 23:59:59"

#### C. 复杂时间表达测试
11. "下个月初开始新项目" → title="开始新项目", startDate="2025-09-01"
12. "下个月初早上开始新项目" → title="开始新项目", startDate="2025-09-01 09:00:00"
13. "这个周末去旅游" → title="去旅游", dueDate="本周六"
14. "这个周末晚上去旅游" → title="去旅游", dueDate="本周六 23:59:59"

#### D. 对比测试（重要）
15. "明天开会" → title="开会", dueDate="2025-08-08" (全天任务)
16. "明天下午开会" → title="开会", dueDate="2025-08-08 15:00:00" (具体时间)
17. "后天完成任务" → title="完成任务", dueDate="2025-08-09" (全天任务)
18. "后天早上完成任务" → title="完成任务", dueDate="2025-08-09 09:00:00" (具体时间)

### 连接词清理测试
19. "计划后天安排会议" → title="会议", dueDate="2025-08-09"
20. "要在本周完成任务" → title="完成任务", dueDate="本周日"

## 预期效果

通过这些修改，AI 应该能够：

1. **准确识别**用户输入中的时间表达
2. **智能清理**任务标题，移除时间关键词和多余连接词
3. **正确转换**时间表达为合适的日期时间参数
4. **时间精度处理**：
   - 包含具体时间段 → 使用 YYYY-MM-DD HH:MM:SS 格式
   - 仅包含日期 → 使用 YYYY-MM-DD 格式（全天任务）
5. **保持标题简洁**，突出核心任务内容
6. **提供友好反馈**，根据时间精度给出相应的确认信息

### 关键改进点：

1. **避免过度具体化**：不再为仅有日期的表达自动添加默认时间
2. **精确时间处理**：只有明确包含时间段的表达才设置具体时间
3. **全天任务支持**：支持创建全天任务，提升用户体验
4. **智能区分**：能够区分"明天开会"（全天）和"明天下午开会"（具体时间）

这样就解决了原来时间关键词被错误包含在任务标题中的问题，同时提供了更精确的时间处理逻辑。
