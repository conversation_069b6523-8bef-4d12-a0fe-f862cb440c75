{"bsonType": "object", "required": ["status"], "permission": {"read": true, "create": true, "update": true, "delete": true}, "properties": {"_id": {"description": "sessionId，前端/服务端生成"}, "status": {"bsonType": "string", "enum": ["running", "completed", "canceled", "failed"]}, "canceled": {"bsonType": ["bool", "int"], "description": "是否被取消"}, "error": {"bsonType": "string"}, "canceledAt": {"bsonType": "string"}, "createTime": {"bsonType": "string"}, "updateTime": {"bsonType": "string"}, "deleteTime": {"bsonType": "string"}}}