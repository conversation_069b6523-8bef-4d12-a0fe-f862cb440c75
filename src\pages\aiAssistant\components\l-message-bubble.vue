<template>
  <div class="message-bubble" :class="{ 'user-bubble': isUser, 'status-message': statusMessage }" @click="handleClick">
    <!-- 文本消息 - 支持多种文本类型 -->
    <div v-if="isTextMessage" class="text-content" :class="{ collapsed: isCollapsed }">
      <template v-if="isUser">
        {{ content }}
      </template>
      <div v-else class="md-row">
        <!-- 流式输入时在开头显示闪烁图标 -->
        <i v-if="streaming" class="fas fa-circle streaming-icon" aria-hidden="true"></i>
        <div class="md-content" v-html="renderedContent"></div>
      </div>
      <div v-if="isCollapsed" class="expand-hint">点击展开</div>
    </div>

    <!-- 工具执行结果消息 -->
    <div v-else-if="type === 'tool_result'" class="tool-result-content">
      <div class="tool-result-header" :class="{ success: toolSuccess, error: !toolSuccess }">
        <i :class="['fas', toolSuccess ? 'fa-check-circle' : 'fa-exclamation-circle']"></i>
        <span>{{ toolName }} {{ toolSuccess ? '执行成功' : '执行失败' }}</span>
      </div>
      <div v-if="toolSuccess && toolResult" class="tool-result-data">
        <!-- 详情区块（默认折叠） -->
        <div v-if="toolResult.details" class="tool-details" :class="{ collapsed: !detailsExpanded }">
          <div class="tool-details-header" @click.stop="toggleDetails">
            <i :class="['fas', detailsExpanded ? 'fa-angle-down' : 'fa-angle-right']"></i>
            <span>详情</span>
            <span class="op-type">（{{ operationTypeLabel }}）</span>
          </div>
          <div v-if="detailsExpanded" class="tool-details-body">
            <div class="row">
              <span class="label">受影响：</span>
              <span>{{ toolResult.details.rowsAffected || 0 }} 条</span>
            </div>
            <!-- create -->
            <template v-if="toolResult.details.operationType === 'create'">
              <div class="row">
                <span class="label">新增：</span>
                <span>{{ toolResult.details.createdCount || toolResult.details.createdIds?.length || 0 }} 条</span>
              </div>
              <div v-if="toolResult.details.preview && toolResult.details.preview.length" class="preview-list">
                <div v-for="(p, i) in toolResult.details.preview" :key="i" class="preview-item">
                  <span class="title">{{ p.title }}</span>
                  <span v-if="p.startDate" class="time">开始：{{ p.startDate }}</span>
                  <span v-if="p.dueDate" class="time">截止：{{ p.dueDate }}</span>
                </div>
              </div>
            </template>
            <!-- update -->
            <template v-else-if="toolResult.details.operationType === 'update'">
              <div v-for="(chg, idx) in toolResult.details.changes || []" :key="idx" class="change-item">
                <div class="row">
                  <span class="label">对象：</span>
                  <span class="title">{{ chg.title || '未命名' }}</span>
                </div>
                <div class="row">
                  <span class="label">变更字段：</span>
                  <span>{{ (chg.changedFields || []).join(', ') }}</span>
                </div>
                <div class="diff-list">
                  <div v-for="(d, field) in chg.diff || {}" :key="field" class="diff-item">
                    <span class="mono field">{{ field }}</span>
                    <span class="mono before">→</span>
                    <span class="after">{{ formatDiffValue(d.after) }}</span>
                  </div>
                </div>
              </div>
            </template>
            <!-- delete -->
            <template v-else-if="toolResult.details.operationType === 'delete'">
              <div class="row">
                <span class="label">删除：</span>
                <span>{{ toolResult.details.deletedCount || toolResult.details.deletedIds?.length || 0 }} 条</span>
              </div>
              <div v-if="toolResult.details.titles && toolResult.details.titles.length" class="title-list">
                <span class="label">涉及：</span>
                <span>{{ toolResult.details.titles.join(', ') }}</span>
              </div>
            </template>
            <div v-if="toolResult.details.truncated" class="truncated-hint">为保证性能，已部分省略</div>
          </div>
        </div>
      </div>
      <div v-if="!toolSuccess && toolError" class="tool-error-message">
        {{ toolError }}
      </div>
    </div>
    <!-- 错误消息 -->
    <div v-else-if="type === 'error'" class="error-content">
      <div class="error-header">
        <i class="fas fa-exclamation-triangle"></i>
        <span>处理出错</span>
      </div>
      <div class="error-message">{{ content }}</div>
    </div>
    <!-- 音频消息 -->
    <div v-else-if="type === 'audio'" class="audio-content" @click="toggleAudioPlay">
      <div class="audio-icon">
        <i :class="['fas', isPlaying ? 'fa-pause' : 'fa-play']"></i>
      </div>
      <div class="audio-duration">{{ isPlaying ? formatRemainingTime() : formatDuration(audioDuration) }}</div>
      <div
        class="text-icon"
        :class="{
          transcribing: isTranscribing,
          clickable: transcribeResult,
          collapsed: !showTranscribeResult,
        }"
        @click="transcribeResult ? toggleTranscribeResult($event) : null"
      >
        文
      </div>
      <!-- 转写结果 -->
      <div v-if="transcribeResult && showTranscribeResult" class="transcribe-result">{{ transcribeResult }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { parseMarkdown } from '@/utils/tools'

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'text', // 'text' 或 'audio'
  },
  audioUrl: {
    type: String,
    default: '',
  },
  audioDuration: {
    type: Number,
    default: 0, // 音频时长（毫秒）
  },
  isUser: {
    type: Boolean,
    default: false,
  },
  streaming: {
    type: Boolean,
    default: false,
  },
  isCollapsed: {
    type: Boolean,
    default: false,
  },
  onToggle: {
    type: Function,
    default: () => {
      /* no op */
    },
  },
  isTranscribing: {
    type: Boolean,
    default: false,
  },
  transcribeResult: {
    type: String,
    default: '',
  },
  statusMessage: {
    type: Boolean,
    default: false,
  },
  // 工具执行结果相关属性
  toolName: {
    type: String,
    default: '',
  },
  toolSuccess: {
    type: Boolean,
    default: false,
  },
  toolResult: {
    type: Object,
    default: () => null,
  },
  toolError: {
    type: String,
    default: '',
  },
  toolExecutionTime: {
    type: Number,
    default: null,
  },
})

const emit = defineEmits(['toggle'])

// 详情折叠状态
const detailsExpanded = ref(false)
const toggleDetails = () => {
  detailsExpanded.value = !detailsExpanded.value
}

const operationTypeLabel = computed(() => {
  const t = props.toolResult?.details?.operationType
  if (t === 'create') return '创建'
  if (t === 'update') return '更新'
  if (t === 'delete') return '删除'
  if (t === 'query') return '查询'
  return '操作'
})

// 已取消 ID 展示，留空占位避免引用报错
const displayIds = computed(() => '')

const formatDiffValue = (v) => {
  if (v === null || v === undefined) return ''
  if (typeof v === 'string') return v
  try {
    return JSON.stringify(v)
  } catch {
    return String(v)
  }
}

// 计算属性：判断是否是文本类型消息
const isTextMessage = computed(() => {
  const textTypes = ['text', 'user', 'ai_streaming', 'ai_complete']
  return textTypes.includes(props.type)
})

// 解析后的 Markdown 内容（用于 AI 文本消息）
const renderedContent = computed(() => {
  if (!props.content) return ''
  return parseMarkdown(props.content)
})

// 音频播放状态
const isPlaying = ref(false)
let audioElement = null
let innerAudioContext = null

// 当前播放时间
const currentTime = ref(0)

// 添加转写结果显示状态
const showTranscribeResult = ref(true)

// 在组件挂载时创建音频元素
onMounted(() => {
  if (props.type === 'audio' && props.audioUrl) {
    // #ifdef H5
    audioElement = new Audio(props.audioUrl)

    // 监听播放结束事件
    audioElement.addEventListener('ended', handleAudioEnded)
    // 错误处理
    audioElement.addEventListener('error', handleAudioError)
    // 监听时间更新
    audioElement.addEventListener('timeupdate', handleTimeUpdate)
    // #endif

    // #ifndef H5
    // App 端使用 uni 的音频 API
    innerAudioContext = uni.createInnerAudioContext()
    innerAudioContext.src = props.audioUrl

    // 监听播放结束事件
    innerAudioContext.onEnded(() => {
      handleAudioEnded()
    })

    // 错误处理
    innerAudioContext.onError((res) => {
      console.error('音频播放错误：', res)
      handleAudioError(res)
    })

    // 监听时间更新
    innerAudioContext.onTimeUpdate(() => {
      currentTime.value = innerAudioContext.currentTime * 1000 // 转换为毫秒
    })
    // #endif
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  // #ifdef H5
  if (audioElement) {
    audioElement.pause()
    audioElement.removeEventListener('ended', handleAudioEnded)
    audioElement.removeEventListener('error', handleAudioError)
    audioElement.removeEventListener('timeupdate', handleTimeUpdate)
    audioElement = null
  }
  // #endif

  // #ifndef H5
  if (innerAudioContext) {
    innerAudioContext.stop()
    innerAudioContext.destroy()
    innerAudioContext = null
  }
  // #endif
})

// 处理时间更新
const handleTimeUpdate = () => {
  // #ifdef H5
  if (audioElement) {
    currentTime.value = audioElement.currentTime * 1000 // 转换为毫秒
  }
  // #endif
}

// 格式化剩余时间
const formatRemainingTime = () => {
  const remaining = Math.max(0, props.audioDuration - currentTime.value)
  const seconds = Math.ceil(remaining / 1000)
  return `${seconds}"`
}

// 切换音频播放/暂停
const toggleAudioPlay = () => {
  // #ifdef H5
  if (!audioElement) {
    console.error('音频元素不存在')
    return
  }

  if (isPlaying.value) {
    // 暂停播放
    audioElement.pause()
    isPlaying.value = false
  } else {
    // 播放前先停止所有其他正在播放的音频
    stopAllOtherAudio()

    // 开始播放
    audioElement.play().catch((error) => {
      console.error('播放音频失败：', error)
    })
    isPlaying.value = true
  }
  // #endif

  // #ifndef H5
  if (!innerAudioContext) {
    console.error('音频元素不存在')
    return
  }

  if (isPlaying.value) {
    // 暂停播放
    innerAudioContext.pause()
    isPlaying.value = false
  } else {
    // 播放前先停止所有其他正在播放的音频
    stopAllOtherAudio()

    // 开始播放
    innerAudioContext.play()
    isPlaying.value = true
  }
  // #endif
}

// 音频播放结束处理
const handleAudioEnded = () => {
  isPlaying.value = false
  currentTime.value = 0 // 重置当前时间
}

// 音频错误处理
const handleAudioError = (error) => {
  console.error('音频播放错误：', error)
  isPlaying.value = false
}

// 停止所有其他音频播放
const stopAllOtherAudio = () => {
  // #ifdef H5
  // 创建一个自定义事件，通知其他音频消息停止播放
  document.dispatchEvent(
    new CustomEvent('stop-all-audio', {
      detail: { except: props.audioUrl },
    })
  )
  // #endif

  // #ifndef H5
  // App 端使用 uni 的事件系统
  uni.$emit('stop-all-audio', { except: props.audioUrl })
  // #endif
}

// 监听全局音频停止事件
onMounted(() => {
  // #ifdef H5
  document.addEventListener('stop-all-audio', handleStopAllAudio)
  // #endif

  // #ifndef H5
  uni.$on('stop-all-audio', handleStopAllAudio)
  // #endif
})

onUnmounted(() => {
  // #ifdef H5
  document.removeEventListener('stop-all-audio', handleStopAllAudio)
  // #endif

  // #ifndef H5
  uni.$off('stop-all-audio', handleStopAllAudio)
  // #endif
})

// 处理停止所有音频的事件
const handleStopAllAudio = (event) => {
  // #ifdef H5
  // 如果当前音频不是例外，则停止播放
  if (audioElement && isPlaying.value && event.detail.except !== props.audioUrl) {
    audioElement.pause()
    isPlaying.value = false
  }
  // #endif

  // #ifndef H5
  // App 端处理
  if (innerAudioContext && isPlaying.value && event.except !== props.audioUrl) {
    innerAudioContext.pause()
    isPlaying.value = false
  }
  // #endif
}

// 格式化音频时长
const formatDuration = (duration) => {
  const seconds = Math.floor(duration / 1000)
  return `${seconds}"`
}

// 处理点击事件
const handleClick = () => {
  if (props.type === 'text' && !props.isUser) {
    props.onToggle()
  }
}

// 切换转写结果显示/隐藏
const toggleTranscribeResult = (event) => {
  event.stopPropagation() // 阻止事件冒泡，避免触发音频播放
  showTranscribeResult.value = !showTranscribeResult.value
}
</script>

<style lang="scss" scoped>
.message-bubble {
  max-width: 70%;
  border-radius: 12px;
  padding: 10px 15px;
  background-color: var(--color-white);
  color: var(--color-gray-800);
  word-break: break-word;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);

  &.user-bubble {
    background-color: var(--color-primary-light);
    color: white;
    border-top-right-radius: 4px;
    border: 1px solid transparent;
  }

  &:not(.user-bubble) {
    border-top-left-radius: 4px;
  }

  &.status-message {
    background-color: #f0f8ff;
    color: #666;
    border-color: #e0e8ef;
    font-style: italic;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--color-primary-light);
      margin-right: 5px;
      animation: status-pulse 1.2s infinite;
      vertical-align: middle;
    }
  }

  // 音频消息样式
  .audio-content {
    display: flex;
    align-items: center;
    min-width: 120px;
    cursor: pointer;
    justify-content: space-between;
    padding: 0 4px;
    position: relative;
    flex-wrap: wrap;

    .text-icon {
      font-size: 14px;
      color: var(--color-gray-600);
      margin-left: 8px;

      &.transcribing {
        animation: transcribing 1s infinite;
      }

      &.clickable {
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }

      &.collapsed {
        opacity: 0.6;
      }
    }

    .transcribe-result {
      width: 100%;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid var(--color-gray-200);
      font-size: 14px;
      color: var(--color-gray-700);
      word-break: break-all;
      cursor: default;
      white-space: pre-line;
    }

    .audio-icon {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: var(--color-gray-700);
    }

    .audio-duration {
      font-size: 14px;
      color: var(--color-gray-700);
      min-width: 36px;
      text-align: right;
    }
  }

  .text-content {
    word-break: break-all;
    white-space: pre-wrap;

    .md-row {
      display: flex;
      align-items: flex-start;
      gap: 6px;
    }

    .streaming-icon {
      font-size: 10px;
      color: var(--color-primary);
      margin-top: 6px;
      animation: icon-blink 1.2s ease-in-out infinite;
    }

    @keyframes icon-blink {
      0%,
      100% {
        opacity: 1;
        transform: scale(1);
      }

      50% {
        opacity: 0.3;
        transform: scale(0.85);
      }
    }

    &.collapsed {
      max-height: 60px;
      overflow: hidden;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: linear-gradient(to bottom, transparent, #fff);
      }
    }

    .expand-hint {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      color: #999;
      font-size: 12px;
      padding: 4px 0;
    }
  }
}

.user-bubble .audio-content {
  .audio-icon {
    color: var(--color-primary-dark);
  }

  .audio-duration {
    color: var(--color-primary-dark);
  }
}

@keyframes transcribing {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.5;
    transform: scale(0.95);
  }
}

/* 旧版光标已移除 */

@keyframes status-pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 工具执行结果消息样式 */
.tool-result-content {
  .tool-result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;

    &.success {
      color: var(--color-success);
    }

    &.error {
      color: var(--color-error);
    }

    i {
      font-size: 16px;
    }
  }

  .tool-result-data {
    .tool-message {
      margin-bottom: 6px;
      line-height: 1.5;
      color: var(--color-gray-700);
    }

    .tool-data-summary {
      padding: 6px 10px;
      background-color: var(--color-primary-light);
      border-radius: 4px;
      font-size: 13px;
      color: var(--color-primary-dark);
      margin-bottom: 6px;
    }

    .tool-execution-time {
      font-size: 12px;
      color: var(--color-gray-500);
    }

    .tool-details {
      margin-top: 8px;
      border: 1px dashed var(--color-gray-300);
      border-radius: 6px;
      overflow: hidden;

      &.collapsed .tool-details-body {
        display: none;
      }

      .tool-details-header {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 8px;
        background: #fafafa;
        cursor: pointer;

        .op-type {
          color: var(--color-gray-600);
          font-size: 12px;
        }
      }

      .tool-details-body {
        max-height: 260px;
        overflow: auto;
        padding: 8px 10px;
        background: white;

        .row {
          margin-bottom: 6px;
          font-size: 13px;
          color: var(--color-gray-700);
        }

        .label {
          color: var(--color-gray-600);
          margin-right: 6px;
        }

        .ids {
          color: var(--color-gray-700);
        }

        .mono {
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
            monospace;
          background: #f6f8fa;
          padding: 1px 4px;
          border-radius: 4px;
        }

        .title {
          margin-left: 6px;
          color: var(--color-gray-700);
        }

        .time {
          margin-left: 10px;
          color: var(--color-gray-600);
          font-size: 12px;
        }

        .preview-list {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .preview-item {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .change-item {
          padding: 6px 0;
          border-top: 1px solid var(--color-gray-100);
        }

        .diff-list {
          margin-top: 4px;
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .diff-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
        }

        .field {
          color: var(--color-gray-700);
        }

        .before {
          color: var(--color-gray-500);
        }

        .after {
          color: var(--color-gray-800);
          word-break: break-all;
        }

        .title-list {
          font-size: 13px;
          color: var(--color-gray-700);
        }

        .truncated-hint {
          margin-top: 8px;
          color: var(--color-gray-500);
          font-size: 12px;
        }
      }
    }
  }

  .tool-error-message {
    padding: 8px 12px;
    background-color: var(--color-error-light);
    border-radius: 6px;
    font-size: 13px;
    color: var(--color-error-dark);
    line-height: 1.4;
  }
}
/* 错误消息样式 */
.error-content {
  .error-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: var(--color-error);
    font-weight: 500;

    i {
      font-size: 16px;
    }
  }

  .error-message {
    line-height: 1.5;
    color: var(--color-error-dark);
  }
}
</style>
